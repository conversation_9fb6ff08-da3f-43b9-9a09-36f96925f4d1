{% extends 'dashboard_app/base_dashboard.html' %}
{% load static %}
{% load i18n %}

{% block title %}Edit Basic Information - {{ venue.venue_name }} - CozyWish{% endblock %}

{% block dashboard_title %}{% endblock %}

{% block sidebar_content %}
    {% include 'dashboard_app/includes/provider_sidebar.html' %}
{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/cozywish_design_system.css' %}">
<style>
    .form-section {
        background: #FFF9F4;
        border-radius: 12px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .form-section-title {
        color: var(--cw-primary);
        font-weight: 600;
        margin-bottom: 1.5rem;
        padding-bottom: 0.75rem;
        border-bottom: 2px solid var(--cw-accent);
    }

    /* Simple category list layout like venue creation */
    .category-list {
        margin-top: 1rem;
    }

    .form-check {
        margin-bottom: 0.75rem;
    }

    .form-check-input {
        margin-right: 0.5rem;
    }

    .form-check-label {
        font-weight: 500;
        color: var(--cw-text);
        cursor: pointer;
    }

    .btn-cw-primary {
        background: var(--cw-primary);
        border-color: var(--cw-primary);
        color: white;
        padding: 0.75rem 3rem;
        font-weight: 500;
        border-radius: 8px;
        transition: all 0.3s ease;
        display: block;
        margin: 2rem auto 0;
        width: fit-content;
    }

    .btn-cw-primary:hover {
        background: var(--cw-primary-light);
        border-color: var(--cw-primary-light);
        color: white;
    }

    .character-count {
        font-size: 0.875rem;
        color: var(--cw-text-muted);
        text-align: right;
        margin-top: 0.25rem;
    }

    .invalid-feedback {
        display: block;
        color: #dc3545;
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }
</style>
{% endblock %}

{% block dashboard_content %}
<div class="dashboard-content">

<!-- Edit Form -->
<form method="post" novalidate>
    {% csrf_token %}
    
    <!-- Basic Details Section -->
    <div class="form-section">
        <h4 class="form-section-title">
            <i class="fas fa-building me-2"></i>Basic Details
        </h4>
        
        <div class="row">
            <div class="col-md-6 mb-3">
                <label for="{{ form.venue_name.id_for_label }}" class="form-label fw-bold">
                    Venue Name <span class="text-danger">*</span>
                </label>
                {{ form.venue_name }}
                {% if form.venue_name.errors %}
                    {% for error in form.venue_name.errors %}
                        <div class="invalid-feedback">{{ error }}</div>
                    {% endfor %}
                {% endif %}
                <div class="character-count">
                    <span id="venue-name-count">0</span>/255 characters
                </div>
            </div>
        </div>
        
        <div class="mb-3">
            <label for="{{ form.short_description.id_for_label }}" class="form-label fw-bold">
                Description <span class="text-danger">*</span>
            </label>
            {{ form.short_description }}
            {% if form.short_description.errors %}
                {% for error in form.short_description.errors %}
                    <div class="invalid-feedback">{{ error }}</div>
                {% endfor %}
            {% endif %}
            <div class="character-count">
                <span id="description-count">0</span>/500 characters
            </div>
            <small class="form-text text-muted">
                Describe your venue and what makes it special. This will be displayed to customers.
            </small>
        </div>
    </div>
    
    <!-- Categories Section -->
    <div class="form-section">
        <h4 class="form-section-title">
            <i class="fas fa-tags me-2"></i>Categories
        </h4>
        
        <p class="text-muted mb-3">
            Select up to 3 categories that best describe your venue. This helps customers find you.
        </p>

        <!-- Simple checkbox list like venue creation -->
        <div class="category-list">
            {% for choice in form.categories %}
                <div class="form-check mb-2">
                    {{ choice.tag }}
                    <label class="form-check-label" for="{{ choice.id_for_label }}">
                        {{ choice.choice_label }}
                    </label>
                </div>
            {% endfor %}
        </div>
        
        {% if form.categories.errors %}
            {% for error in form.categories.errors %}
                <div class="invalid-feedback mt-2">{{ error }}</div>
            {% endfor %}
        {% endif %}
    </div>
    
    <!-- Contact Information Section -->
    <div class="form-section">
        <h4 class="form-section-title">
            <i class="fas fa-phone me-2"></i>Contact Information
        </h4>
        
        <div class="row">
            <div class="col-md-6 mb-3">
                <label for="{{ form.phone.id_for_label }}" class="form-label fw-bold">
                    Phone Number
                </label>
                {{ form.phone }}
                {% if form.phone.errors %}
                    {% for error in form.phone.errors %}
                        <div class="invalid-feedback">{{ error }}</div>
                    {% endfor %}
                {% endif %}
                <small class="form-text text-muted">
                    Primary contact number for your venue
                </small>
            </div>
            
            <div class="col-md-6 mb-3">
                <label for="{{ form.email.id_for_label }}" class="form-label fw-bold">
                    Email Address
                </label>
                {{ form.email }}
                {% if form.email.errors %}
                    {% for error in form.email.errors %}
                        <div class="invalid-feedback">{{ error }}</div>
                    {% endfor %}
                {% endif %}
                <small class="form-text text-muted">
                    Primary contact email for your venue
                </small>
            </div>
        </div>
        
        <div class="mb-3">
            <label for="{{ form.website_url.id_for_label }}" class="form-label fw-bold">
                Website URL
            </label>
            {{ form.website_url }}
            {% if form.website_url.errors %}
                {% for error in form.website_url.errors %}
                    <div class="invalid-feedback">{{ error }}</div>
                {% endfor %}
            {% endif %}
            <small class="form-text text-muted">
                Your venue's website (optional)
            </small>
        </div>
    </div>
    
    <!-- Form Actions -->
    <div class="text-center">
        <button type="submit" class="btn btn-cw-primary">
            <i class="fas fa-save me-2"></i>Update
        </button>
    </div>
</form>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Character counting
    function updateCharacterCount(input, countElement, maxLength) {
        const count = input.value.length;
        countElement.textContent = count;
        
        if (count > maxLength * 0.9) {
            countElement.style.color = '#dc3545';
        } else if (count > maxLength * 0.7) {
            countElement.style.color = '#ffc107';
        } else {
            countElement.style.color = '#6c757d';
        }
    }
    
    // Venue name character count
    const venueNameInput = document.getElementById('{{ form.venue_name.id_for_label }}');
    const venueNameCount = document.getElementById('venue-name-count');
    if (venueNameInput && venueNameCount) {
        updateCharacterCount(venueNameInput, venueNameCount, 255);
        venueNameInput.addEventListener('input', function() {
            updateCharacterCount(this, venueNameCount, 255);
        });
    }
    
    // Description character count
    const descriptionInput = document.getElementById('{{ form.short_description.id_for_label }}');
    const descriptionCount = document.getElementById('description-count');
    if (descriptionInput && descriptionCount) {
        updateCharacterCount(descriptionInput, descriptionCount, 500);
        descriptionInput.addEventListener('input', function() {
            updateCharacterCount(this, descriptionCount, 500);
        });
    }
    
    // Simple category selection with limit enforcement (like venue creation)
    function setupCategorySelection() {
        document.querySelectorAll('.category-list input[type="checkbox"]').forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                const selectedCount = document.querySelectorAll('.category-list input[type="checkbox"]:checked').length;

                // Check selection limit (3 categories max)
                if (selectedCount > 3) {
                    this.checked = false;
                    alert('You can select up to 3 categories only.');
                    return;
                }
            });
        });
    }

    // Initialize category selection
    setupCategorySelection();
});
</script>
{% endblock %}
